#!/usr/bin/env python3
"""
Test YAML parsing with custom tags in PyTorch environment
"""
import sys
import os
import yaml

# Add project path
sys.path.insert(0, '/home/<USER>/code/RGB-X/DAMSDet')

try:
    # Import the yaml_helpers which should register the custom constructors
    from ppdet.core.config.yaml_helpers import setup_orderdict
    setup_orderdict()
    
    # Import the classes to trigger @serializable registration
    from ppdet.optimizer.optimizer import PiecewiseDecay, LinearWarmup
    
    print("✅ 成功导入 PiecewiseDecay 和 LinearWarmup 类")
    
    # Try to parse a YAML file with custom tags
    yaml_content = """
LearningRate:
  base_lr: 0.0001
  schedulers:
  - !PiecewiseDecay
    gamma: 1.0
    milestones: [100]
    use_warmup: true
  - !LinearWarmup
    start_factor: 0.001
    steps: 2000
"""
    
    print("🔍 测试 YAML 解析...")
    
    # Parse the YAML
    config = yaml.load(yaml_content, Loader=yaml.Loader)
    
    print("✅ YAML 解析成功!")
    print(f"📊 解析结果: {config}")
    
    # Check if the custom objects were created correctly
    schedulers = config['LearningRate']['schedulers']
    print(f"📋 调度器数量: {len(schedulers)}")
    
    for i, scheduler in enumerate(schedulers):
        print(f"   调度器 {i+1}: {scheduler}")
        print(f"   类型: {type(scheduler)}")
        if hasattr(scheduler, '__dict__'):
            print(f"   参数: {scheduler.__dict__}")
    
    print("\n🎉 YAML 自定义标签解析测试通过!")
    
except Exception as e:
    print(f"❌ YAML 解析失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)