
# DAMSDet PyTorch 迁移技术规格文档

## 1. 需求概述
*   **目标**: 将基于 PaddlePaddle 的 DAMSDet 项目完全迁移到 PyTorch 生态，消除对 PaddlePaddle 的所有依赖，并保持原有模型的核心功能与性能。

## 2. 实施大纲
1.  **项目结构搭建**: 创建新的符合 PyTorch 社区规范的目录结构。
2.  **核心组件转换**: 逐个将模型的关键模块（骨干网络、变压器、检测头、损失函数）从 PaddlePaddle API 转换为 PyTorch API。
3.  **CUDA 算子替换**: 使用 PyTorch 生态中的成熟库替换所有自定义的 CUDA 算子。
4.  **数据管道重建**: 使用 `torch.utils.data.Dataset` 和 `DataLoader` 重建数据加载和预处理流程。
5.  **训练与评估脚本重写**: 基于 PyTorch 重写训练、评估和推理的主脚本。
6.  **配置系统改造**: 废弃 PaddlePaddle 的 YAML 配置系统，采用纯 Python 类进行配置管理。

## 3. 具体变更方案

### 3.1. 代码组织和文件结构
采纳需求文档中建议的目录结构，具体如下：

```
damsdet_pytorch/
├── models/
│   ├── __init__.py
│   ├── damsdet.py              # [新] 主架构 (torch.nn.Module)
│   ├── transformer.py          # [新] DAMS 变压器
│   ├── attention.py            # [新] 可变形注意力模块
│   ├── backbone.py             # [新] ResNet 双骨干网络
│   ├── head.py                 # [新] 检测头
│   └── loss.py                 # [新] 损失函数 (含匈牙利匹配)
├── data/
│   ├── __init__.py
│   ├── dataset.py              # [新] 多光谱 COCO 数据集类
│   ├── transforms.py           # [新] 数据增强 (支持双图像同步)
│   └── collate.py              # [新] 自定义 Collate Function
├── ops/
│   └── box_iou_rotated.py      # [新] 旋转框计算 (若不引入外部依赖)
├── configs/                    # [新] 基于 Python 类的配置文件
│   ├── base_config.py
│   └── damsdet_r50_m3fd.py
├── tools/
│   ├── train.py                # [新] 训练脚本
│   ├── eval.py                 # [新] 评估脚本
│   └── infer.py                # [新] 推理脚本
├── utils/
│   ├── misc.py                 # [新] 工具函数
│   └── vis.py                  # [新] 可视化
├── requirements.txt            # [新] PyTorch 依赖
└── README.md
```

### 3.2. 核心组件技术规格

#### **文件: `models/backbone.py`**
*   **目的**: 实现支持双模态输入的 ResNet 骨干网络。
*   **核心实现**:
    *   创建一个 `DualResNet` 模块，内部包含两个独立的 ResNet 实例（`backbone_vis` 和 `backbone_ir`）。
    *   推荐使用 `torchvision.models.resnet50` 作为基础，并加载预训练权重。
    *   通过 `torch.hub.load` 或直接使用 `torchvision.models` 获取模型，并重写 `forward` 方法。
    *   `forward(images)` 方法接受一个字典 `{'vis_image': tensor, 'ir_image': tensor}`。
    *   返回两个分支提取的多尺度特征图列表 `vis_feats`, `ir_feats`。
    *   移除原 `resnet.py` 中复杂的 `flag` 参数逻辑，使接口更清晰。

#### **文件: `models/attention.py`**
*   **目的**: 封装可变形注意力（Deformable Attention）的实现。
*   **核心实现**:
    *   实现一个 `DeformableAttention` 模块。
    *   **方案**: 引入 `torchvision.ops.DeformableAttention` 作为首选方案。如果其功能不完全满足（例如，缺少 key-aware 变体），则考虑从 `mmcv` 或其他知名库中借鉴实现。
    *   接口定义: `forward(query, reference_points, value, value_spatial_shapes, value_level_start_index, value_mask)`，与原版保持一致。

#### **文件: `models/transformer.py`**
*   **目的**: 实现 DAMS 变压器，包含竞争性查询选择机制。
*   **核心实现**:
    *   `DAMSTransformer(torch.nn.Module)`:
        *   `__init__`: 初始化编码器头（`enc_score_head`, `enc_bbox_head`）、解码器层、去噪（Denoising）相关嵌入层。
        *   `forward`:
            1.  对融合后的多光谱特征 `visir_feats` 进行投影。
            2.  **实现竞争性查询选择**:
                *   将多尺度特征输入 `enc_output` 层。
                *   通过 `enc_score_head` 和 `enc_bbox_head` 计算初步的类别得分和边界框。
                *   对类别得分执行 `torch.topk`，获取得分最高的 `num_queries` 个 proposal 的索引 `topk_ind`。
                *   使用 `torch.gather` 根据 `topk_ind` 从 encoder 的输出中提取特征和 reference points，作为 decoder 的输入。
            3.  **实现去噪**: 如果是训练模式，生成带噪声的 GT boxes 并与 `topk` 选出的查询拼接。
            4.  调用 `TransformerDecoder`。
    *   `TransformerDecoder(torch.nn.Module)`:
        *   包含多层 `TransformerDecoderLayer`。
        *   循环执行解码层，并在每层后预测中间结果，实现层级监督。

#### **文件: `models/damsdet.py`**
*   **目的**: 整合所有模块，构建完整的 DAMSDet 模型。
*   **核心实现**:
    *   `DAMSDet(torch.nn.Module)`:
        *   `__init__`: 实例化 `DualResNet`、`DAMSTransformer`、`DETRHead` 和可选的 `Neck` 模块。不再使用字符串和 `create` 函数，而是直接实例化。
        *   `forward`: 编排整个前向传播流程，与原版 `_forward` 逻辑一致。
            1.  `vis_feats = self.backbone.vis_forward(inputs['vis_image'])`
            2.  `ir_feats = self.backbone.ir_forward(inputs['ir_image'])`
            3.  融合特征 `visir_feats = vis_feats + ir_feats`。
            4.  `transformer_outputs = self.transformer(visir_feats)`。
            5.  `outputs = self.head(transformer_outputs)`。
            6.  训练时，将 `outputs` 和 `targets` 传给 `Loss` 模块计算损失；推理时，进行后处理。

### 3.3. API 设计

*   **配置系统 (`configs/`)**:
    *   使用纯 Python 类来管理配置，例如 `fvcore` 的 `CfgNode` 或一个简单的自定义字典类。
    *   `base_config.py` 定义所有默认参数。
    *   特定实验的配置（如 `damsdet_r50_m3fd.py`）继承基础配置并覆盖所需参数。
    *   优点：利用 IDE 的代码提示、易于静态分析、配置与代码逻辑关联更紧密。

*   **数据加载 (`data/`)**:
    *   `MultiModalCocoDataset(torch.utils.data.Dataset)`:
        *   `__init__`: 加载 COCO 格式的 anntation JSON 文件。
        *   `__getitem__(self, idx)`: 根据索引 `idx` 读取一张可见光图像和对应的红外图像，以及它们的标注。返回一个字典：`{'vis_image': img, 'ir_image': img, 'annotations': [...]}`。
    *   `transforms.py`: 实现一系列数据增强类，如 `RandomHorizontalFlip`, `Resize`。每个变换类的 `__call__` 方法需要能同时处理 `vis_image` 和 `ir_image`，确保几何变换的一致性。
    *   `collate.py`: 实现 `collate_fn` 函数，用于将批次中的样本打包成一个 batch 张量，并处理图像 padding，生成 `pad_mask`。

### 3.4. 依赖库选择
*   **核心**: `torch>=2.0`, `torchvision>=0.15`。
*   **可变形注意力**: 优先使用 `torchvision.ops.DeformableAttention`。
*   **旋转框 NMS/IoU**:
    *   **首选方案**: 引入 `mmrotate` 或 `torchvision.ops.box_iou_rotated` (如果可用)。这能最大化地利用现有轮子，降低开发成本。
    *   **备选方案**: 如果不想引入大型依赖，可以寻找轻量级的纯 PyTorch 实现，或将 `ppdet/ext_op` 中的 CUDA 核心逻辑适配为 PyTorch 的 C++ 扩展。此方案风险高，耗时较长。
*   **评估**: `pycocotools` 用于计算 COCO mAP。
*   **配置**: 无需特定库，使用 Python 原生类或 `yacs` / `fvcore`。

## 4. 关键点与讨论项

*   **[❗ 难点] 旋转框算子的替换**:
    *   `ppdet/ext_op/` 下的 `rbox_iou` 和 `nms_rotated` 是自定义 CUDA 实现，用于处理旋转目标框。这是整个迁移工作中技术难度最高的部分。
    *   直接翻译 CUDA 代码到 PyTorch C++ Extension 耗时且容易出错。

*   **[⚠️ 风险] 性能对齐**:
    *   即使所有模块都被正确转换，由于框架底层实现（如卷积算法选择、内存管理）的差异，最终模型的训练速度和推理性能可能与原版存在偏差。
    *   需要进行严格的基准测试，并可能需要微调超参数（如学习率、优化器参数）来弥补差距。

*   **[❓ 决策点 1] 如何处理旋转框计算?**
    *   **方案A：引入外部依赖**。集成 `mmrotate` 或其他专门处理旋转检测的库。
        *   **优点**: 快速、可靠，可以利用成熟社区的维护。
        *   **缺点**: 增加项目的依赖复杂性，可能引入大量不需要的功能。
    *   **方案B：自研或适配轻量级实现**。寻找一个轻量级的、纯 PyTorch 实现的旋转框 IoU/NMS 库，或者将原有的 CUDA 代码适配给 PyTorch。
        *   **优点**: 保持项目轻量、依赖干净。
        *   **缺点**: 开发和测试工作量大，需要处理 CUDA 编译等底层问题，风险较高。
    *   **建议**: 启动项目时，优先选择 **方案A** 以快速推进核心功能的迁移。在后期优化阶段，如果依赖问题显著，再考虑方案B。

*   **[❓ 决策点 2] 配置系统选择？**
    *   **方案A：纯 Python 类/字典**。
        *   **优点**: 零依赖，对开发者友好，易于调试和扩展。
        *   **缺点**: 配置与代码逻辑耦合较紧。
    *   **方案B：YAML + Hydra/Argparse**。
        *   **优点**: 配置与代码分离，方便管理大量实验。
        *   **缺点**: 增加了学习成本和依赖。
    *   **建议**: 鉴于本项目核心是模型迁移而非大规模实验平台，推荐 **方案A**，它更简洁直观。

*   **[❓ 决策点 3] 预训练权重如何迁移?**
    *   PaddlePaddle 和 PyTorch 的权重命名规则和存储格式不同（例如，`conv.weight` vs `conv._weight`，BN层的 `_mean` `_variance` vs `running_mean` `running_var`）。
    *   需要编写一个专门的转换脚本，读取 `.pdparams` 文件，根据新的模型结构调整权重张量的名称和形状（如 `transpose`），然后保存为 `.pth` 文件。这是一个细致但必要的工作。
    *   **建议**: 在模型结构基本稳定后，立即着手编写此脚本，以便尽早验证模型转换的正确性。
