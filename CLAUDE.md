# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

DAMSDet 是一个基于 PaddlePaddle 实现的动态自适应多光谱检测变压器。它专为使用多光谱（可见光和红外）图像对进行目标检测而设计。该项目实现了一个新颖的基于变压器的架构，具有竞争性查询选择和自适应特征融合机制。

## 开发环境

- **框架**: PaddlePaddle 2.5 (Stable) 配合 CUDA 11.7
- **Python**: 3.8
- **依赖项**: 完整列表请参见 `requirements.txt`
- **安装**: 基于 PaddleDetection，需要正确的环境设置

## 关键命令

### 训练
使用相应的配置文件在不同数据集上训练：
```bash
# M3FD 数据集
python tools/train.py -c configs/damsdet/damsdet_r50vd_m3fd.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval

# FLIR 数据集  
python tools/train.py -c configs/damsdet/damsdet_r50vd_flir.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval

# LLVIP 数据集
python tools/train.py -c configs/damsdet/damsdet_r50vd_llvip.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval

# VEDAI 数据集
python tools/train.py -c configs/damsdet/damsdet_r50vd_vedai.yml -o pretrain_weights=coco_pretrain_weights.pdparams --eval
```

### 评估
在不同数据集上评估模型：
```bash
# M3FD 评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_m3fd.yml --classwise -o weights=output/M3FD/damsdet_r50vd_m3fd/best_model

# FLIR 评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_flir.yml --classwise -o weights=output/FLIR/damsdet_r50vd_flir/best_model

# LLVIP 评估  
python tools/eval.py -c configs/damsdet/damsdet_r50vd_llvip.yml --classwise -o weights=output/LLVIP/damsdet_r50vd_llvip/best_model

# VEDAI 评估
python tools/eval.py -c configs/damsdet/damsdet_r50vd_vedai.yml --classwise -o weights=output/VEDAI/damsdet_r50vd_vedai/best_model
```

### 推理
在验证集上运行推理：
```bash
# M3FD 推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_m3fd.yml --infer_vis_dir=dataset/coco_m3fd/val_vis_img/ --infer_ir_dir=dataset/coco_m3fd/val_ir_img --output_dir=(检测保存路径) -o weights=output/M3FD/damsdet_r50vd_m3fd/best_model

# FLIR 推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_flir.yml --infer_vis_dir=dataset/coco_FLIR_align/val_imgs/vis_imgs --infer_ir_dir=dataset/coco_FLIR_align/val_imgs/ir_imgs --output_dir=(检测保存路径) -o weights=output/M3FD/damsdet_r50vd_m3fd/best_model

# LLVIP 推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_llvip.yml --infer_vis_dir=dataset/coco_LLVIP/val_imgs/vis_imgs --infer_ir_dir=dataset/coco_LLVIP/val_imgs/ir_imgs --output_dir=(检测保存路径) -o weights=output/LLVIP/damsdet_r50vd_llvip/best_model

# VEDAI 推理
python tools/multi_infer.py -c configs/damsdet/damsdet_r50vd_vedai.yml --infer_vis_dir=dataset/coco_VEDAI/val_imgs/vis_imgs --infer_ir_dir=dataset/coco_VEDAI/val_imgs/ir_imgs --output_dir=(检测保存路径) -o weights=output/LLVIP/damsdet_r50vd_llvip/best_model
```

### 包安装
以开发模式安装包：
```bash
pip install -e .
```

## 架构概述

### 核心组件

1. **DAMSDet 架构** (`ppdet/modeling/architectures/damsdet.py`):
   - 实现多光谱检测的主要模型类
   - 处理双骨干网络（可见光和红外）
   - 集成变压器和检测头

2. **DAMS 变压器** (`ppdet/modeling/transformers/damsdetr_transformer.py`):
   - 具有可变形注意力的自定义变压器实现
   - 支持多光谱特征融合
   - 实现竞争性查询选择机制
   - 包含去噪训练功能

3. **数据处理** (`ppdet/data/`):
   - 多光谱数据的 COCO 风格数据集加载器
   - 自定义数据变换和增强
   - 支持多种数据集格式（M3FD、FLIR、LLVIP、VEDAI）

### 关键特性

- **双骨干网络架构**: 可见光和红外图像的单独处理
- **自适应特征融合**: 多光谱特征的动态融合机制
- **竞争性查询选择**: 变压器解码器中的高级查询选择策略
- **可变形注意力**: 多尺度可变形注意力机制
- **去噪训练**: 用于提高性能的对比去噪训练

## 数据集结构

项目支持四个主要数据集：
- **M3FD**: 多光谱行人检测数据集
- **FLIR**: 热成像-可见光对齐数据集  
- **LLVIP**: 低光可见光和红外配对数据集
- **VEDAI**: 航拍图像中的车辆检测数据集

每个数据集都遵循 COCO 风格格式，具有单独的可见光和红外图像目录，相应的注释文件位于 `dataset/coco_*/annotations/`。

## 配置系统

配置文件组织在 `configs/` 目录中：
- `configs/damsdet/`: 模型架构配置
- `configs/datasets/`: 数据集特定配置  
- `configs/runtime_*.yml`: 不同数据集的运行时配置

## 模型权重

预训练权重可供下载：
- COCO 预训练权重：所需的基础模型
- 数据集特定预训练权重：可用于 M3FD、FLIR、LLVIP 和 VEDAI

## 开发注意事项

- 代码库构建在 PaddleDetection 框架之上
- 使用 PaddlePaddle 的动态图模式进行训练
- 支持单 GPU 和多 GPU 训练
- 自定义 CUDA 操作在 `ppdet/ext_op/` 中实现以优化性能
- 模型导出和部署工具在 `tools/` 中可用
- 在原有代码上修改
- 不要新建pytorch文件！！！
- 不要创建新的pytorch项目目录
- 编译运行的标准是，该目录下的__pycache__是否有相应的pyc文件！
