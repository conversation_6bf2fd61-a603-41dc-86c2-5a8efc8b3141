# Temporary PaddlePaddle dependency for migration period
paddlepaddle-gpu>=2.5.0

# Core PyTorch dependencies
torch>=2.0.0
torchvision>=0.22.0
torchaudio>=2.0.0

# Numerical computing
numpy>=1.21.0,<1.27.0
tqdm
typeguard
visualdl>=2.2.0
opencv-python>=4.8.0
PyYAML
shapely
scipy
terminaltables
Cython
pycocotools
setuptools

# Additional PyTorch ecosystem
# torch-audio  # Optional, uncomment if needed
# torch-text   # Optional, uncomment if needed

# Visualization and logging
visualdl>=2.2.0
# tensorboard>=2.13.0  # Alternative to visualdl

# for MOT evaluation and inference
lap
motmetrics
scikit-learn>=1.0.0

# Additional utilities
matplotlib>=3.5.0  # For plotting and visualization
seaborn>=0.11.0   # For statistical visualizations
pandas>=1.3.0      # For data manipulation
Pillow>=9.0.0      # For image processing
requests>=2.25.0   # For HTTP requests
# albumentations>=1.3.0  # For data augmentation (optional)

# for vehicleplate in deploy/pipeline/ppvehicle
pyclipper

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0     # Code formatting
flake8>=5.0.0     # Code linting
isort>=5.10.0     # Import sorting

# Documentation (optional)
# sphinx>=5.0.0
# sphinx-rtd-theme>=1.0.0
