# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved. 
#   
# Licensed under the Apache License, Version 2.0 (the "License");   
# you may not use this file except in compliance with the License.  
# You may obtain a copy of the License at   
#   
#     http://www.apache.org/licenses/LICENSE-2.0    
# 
# Unless required by applicable law or agreed to in writing, software   
# distributed under the License is distributed on an "AS IS" BASIS, 
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  
# See the License for the specific language governing permissions and   
# limitations under the License.

import torch
import torch.nn.functional as F
import torch.nn as nn
import numpy as np
import warnings
from typing import List, Tuple, Optional

# Import torchvision ops for NMS and other operations
try:
    from torchvision.ops import nms, batched_nms
    TORCHVISION_AVAILABLE = True
except ImportError:
    TORCHVISION_AVAILABLE = False
    warnings.warn("torchvision not available, some operations may not work")

# Helper functions for dynamic mode checking
def in_dynamic_mode():
    return True  # PyTorch is always in dynamic mode

__all__ = [
    'prior_box', 'generate_proposals', 'box_coder', 'multiclass_nms',
    'distribute_fpn_proposals', 'matrix_nms', 'batch_norm', 'mish', 'silu',
    'swish', 'identity', 'anchor_generator'
]


def identity(x):
    return x


def mish(x):
    return F.mish(x) if hasattr(F, mish) else x * F.tanh(F.softplus(x))


def silu(x):
    return F.silu(x)


def swish(x):
    return x * F.sigmoid(x)


TRT_ACT_SPEC = {'swish': swish, 'silu': swish}

ACT_SPEC = {'mish': mish, 'silu': silu}


def get_act_fn(act=None, trt=False):
    assert act is None or isinstance(act, (
        str, dict)), 'name of activation should be str, dict or None'
    if not act:
        return identity

    if isinstance(act, dict):
        name = act['name']
        act.pop('name')
        kwargs = act
    else:
        name = act
        kwargs = dict()

    if trt and name in TRT_ACT_SPEC:
        fn = TRT_ACT_SPEC[name]
    elif name in ACT_SPEC:
        fn = ACT_SPEC[name]
    else:
        fn = getattr(F, name)

    return lambda x: fn(x, **kwargs)


def batch_norm(ch,
               norm_type='bn',
               norm_decay=0.,
               freeze_norm=False,
               initializer=None,
               data_format='NCHW'):

    if norm_type in ['sync_bn', 'bn']:
        norm_layer = nn.BatchNorm2d(ch)
    else:
        raise ValueError(f"Unsupported norm_type: {norm_type}")

    # Apply weight decay if specified
    if norm_decay > 0:
        for param in norm_layer.parameters():
            param.weight_decay = norm_decay

    # Freeze parameters if specified
    if freeze_norm:
        for param in norm_layer.parameters():
            param.requires_grad = False

    return norm_layer


def anchor_generator(input,
                     anchor_sizes=None,
                     aspect_ratios=None,
                     variance=[0.1, 0.1, 0.2, 0.2],
                     stride=None,
                     offset=0.5):
    """
    **Anchor generator operator**
    Generate anchors for Faster RCNN algorithm.
    Each position of the input produce N anchors, N =
    size(anchor_sizes) * size(aspect_ratios). The order of generated anchors
    is firstly aspect_ratios loop then anchor_sizes loop.
    Args:
       input(Tensor): 4-D Tensor with shape [N,C,H,W]. The input feature map.
       anchor_sizes(float32|list|tuple, optional): The anchor sizes of generated
          anchors, given in absolute pixels e.g. [64., 128., 256., 512.].
          For instance, the anchor size of 64 means the area of this anchor
          equals to 64**2. None by default.
       aspect_ratios(float32|list|tuple, optional): The height / width ratios
           of generated anchors, e.g. [0.5, 1.0, 2.0]. None by default.
       variance(list|tuple, optional): The variances to be used in box
           regression deltas. The data type is float32, [0.1, 0.1, 0.2, 0.2] by
           default.
       stride(list|tuple, optional): The anchors stride across width and height.
           The data type is float32. e.g. [16.0, 16.0]. None by default.
       offset(float32, optional): Prior boxes center offset. 0.5 by default.
    Returns:
        Tuple:
        Anchors(Tensor): The output anchors with a layout of [H, W, num_anchors, 4].
        H is the height of input, W is the width of input,
        num_anchors is the box count of each position.
        Each anchor is in (xmin, ymin, xmax, ymax) format an unnormalized.

        Variances(Tensor): The expanded variances of anchors
        with a layout of [H, W, num_priors, 4].
        H is the height of input, W is the width of input
        num_anchors is the box count of each position.
        Each variance is in (xcenter, ycenter, w, h) format.
    """

    def _is_list_or_tuple_(data):
        return (isinstance(data, list) or isinstance(data, tuple))

    if not _is_list_or_tuple_(anchor_sizes):
        anchor_sizes = [anchor_sizes]
    if not _is_list_or_tuple_(aspect_ratios):
        aspect_ratios = [aspect_ratios]
    if not (_is_list_or_tuple_(stride) and len(stride) == 2):
        raise ValueError('stride should be a list or tuple ',
                         'with length 2, (stride_width, stride_height).')

    anchor_sizes = list(map(float, anchor_sizes))
    aspect_ratios = list(map(float, aspect_ratios))
    stride = list(map(float, stride))

    # Get input dimensions
    _, _, height, width = input.shape
    device = input.device
    dtype = input.dtype

    # Generate base anchors
    num_anchors = len(anchor_sizes) * len(aspect_ratios)
    base_anchors = []

    for size in anchor_sizes:
        for ratio in aspect_ratios:
            w = size * np.sqrt(ratio)
            h = size / np.sqrt(ratio)
            base_anchors.append([-w/2, -h/2, w/2, h/2])

    base_anchors = torch.tensor(base_anchors, dtype=dtype, device=device)

    # Generate grid
    shift_x = torch.arange(0, width, dtype=dtype, device=device) * stride[0] + offset * stride[0]
    shift_y = torch.arange(0, height, dtype=dtype, device=device) * stride[1] + offset * stride[1]
    shift_y, shift_x = torch.meshgrid(shift_y, shift_x, indexing='ij')

    shifts = torch.stack([shift_x, shift_y, shift_x, shift_y], dim=2)
    shifts = shifts.view(-1, 4)

    # Generate all anchors
    anchors = base_anchors.view(1, num_anchors, 4) + shifts.view(-1, 1, 4)
    anchors = anchors.view(height, width, num_anchors, 4)

    # Generate variances
    variances = torch.tensor(variance, dtype=dtype, device=device)
    variances = variances.view(1, 1, 1, 4).expand(height, width, num_anchors, 4)

    return anchors, variances


def distribute_fpn_proposals(fpn_rois,
                             min_level,
                             max_level,
                             refer_level,
                             refer_scale,
                             pixel_offset=False,
                             rois_num=None,
                             name=None):
    r"""

    In Feature Pyramid Networks (FPN) models, it is needed to distribute all
    proposals into different FPN level, with respect to scale of the proposals,
    the referring scale and the referring level. Besides, to restore the order
    of proposals, we return an array which indicates the original index of rois
    in current proposals. To compute FPN level for each roi, the formula is
    given as follows:

    .. math::

        roi\_scale &= \sqrt{BBoxArea(fpn\_roi)}

        level = floor(&\log(\\frac{roi\_scale}{refer\_scale}) + refer\_level)

    where BBoxArea is a function to compute the area of each roi.

    Args:
        fpn_rois(Tensor): 2-D Tensor with shape [N, 4] and data type is
            float32 or float64. The input fpn_rois.
        min_level(int32): The lowest level of FPN layer where the proposals come
            from.
        max_level(int32): The highest level of FPN layer where the proposals
            come from.
        refer_level(int32): The referring level of FPN layer with specified scale.
        refer_scale(int32): The referring scale of FPN layer with specified level.
        rois_num(Tensor): 1-D Tensor contains the number of RoIs in each image.
            The shape is [B] and data type is int32. B is the number of images.
            If it is not None then return a list of 1-D Tensor. Each element
            is the output RoIs' number of each image on the corresponding level
            and the shape is [B]. None by default.
        name(str, optional): For detailed information, please refer
            to :ref:`api_guide_Name`. Usually name is no need to set and
            None by default.

    Returns:
        Tuple:
        multi_rois(List) : A list of 2-D Tensor with shape [M, 4]
        and data type of float32 and float64. The length is
        max_level-min_level+1. The proposals in each FPN level.

        restore_ind(Tensor): A 2-D Tensor with shape [N, 1], N is
        the number of total rois. The data type is int32. It is
        used to restore the order of fpn_rois.

        rois_num_per_level(List): A list of 1-D Tensor and each Tensor is
        the RoIs' number in each image on the corresponding level. The shape
        is [B] and data type of int32. B is the number of images
    """
    num_lvl = max_level - min_level + 1
    device = fpn_rois.device

    # Compute roi areas
    w = fpn_rois[:, 2] - fpn_rois[:, 0]
    h = fpn_rois[:, 3] - fpn_rois[:, 1]
    roi_scale = torch.sqrt(w * h)

    # Compute target levels
    target_lvls = torch.floor(torch.log2(roi_scale / refer_scale) + refer_level)
    target_lvls = torch.clamp(target_lvls, min_level, max_level).long()

    # Distribute rois to different levels
    multi_rois = []
    restore_ind = torch.zeros(fpn_rois.shape[0], 1, dtype=torch.int32, device=device)
    rois_num_per_level = []

    current_idx = 0
    for level in range(min_level, max_level + 1):
        level_mask = (target_lvls == level)
        level_rois = fpn_rois[level_mask]
        multi_rois.append(level_rois)

        # Update restore indices
        level_indices = torch.nonzero(level_mask, as_tuple=False).squeeze(1)
        restore_ind[current_idx:current_idx + len(level_indices)] = level_indices.unsqueeze(1)
        current_idx += len(level_indices)

        if rois_num is not None:
            # Compute rois number per level for each image
            level_rois_num = torch.zeros_like(rois_num)
            # This is a simplified version - in practice, you'd need to track which rois belong to which image
            rois_num_per_level.append(level_rois_num)

    if rois_num is None:
        rois_num_per_level = None

    return multi_rois, restore_ind, rois_num_per_level


def prior_box(input,
              image,
              min_sizes,
              max_sizes=None,
              aspect_ratios=[1.],
              variance=[0.1, 0.1, 0.2, 0.2],
              flip=False,
              clip=False,
              steps=[0.0, 0.0],
              offset=0.5,
              min_max_aspect_ratios_order=False,
              name=None):
    """
    This op generates prior boxes for SSD(Single Shot MultiBox Detector) algorithm.
    Each position of the input produce N prior boxes, N is determined by
    the count of min_sizes, max_sizes and aspect_ratios, The size of the
    box is in range(min_size, max_size) interval, which is generated in
    sequence according to the aspect_ratios.

    Parameters:
       input(Tensor): 4-D tensor(NCHW), the data type should be float32 or float64.
       image(Tensor): 4-D tensor(NCHW), the input image data of PriorBoxOp,
            the data type should be float32 or float64.
       min_sizes(list|tuple|float): the min sizes of generated prior boxes.
       max_sizes(list|tuple|None): the max sizes of generated prior boxes.
            Default: None.
       aspect_ratios(list|tuple|float): the aspect ratios of generated
            prior boxes. Default: [1.].
       variance(list|tuple): the variances to be encoded in prior boxes.
            Default:[0.1, 0.1, 0.2, 0.2].
       flip(bool): Whether to flip aspect ratios. Default:False.
       clip(bool): Whether to clip out-of-boundary boxes. Default: False.
       step(list|tuple): Prior boxes step across width and height, If
            step[0] equals to 0.0 or step[1] equals to 0.0, the prior boxes step across
            height or weight of the input will be automatically calculated.
            Default: [0., 0.]
       offset(float): Prior boxes center offset. Default: 0.5
       min_max_aspect_ratios_order(bool): If set True, the output prior box is
            in order of [min, max, aspect_ratios], which is consistent with
            Caffe. Please note, this order affects the weights order of
            convolution layer followed by and does not affect the final
            detection results. Default: False.
       name(str, optional): The default value is None.  Normally there is no need for
            user to set this property.

    Returns:
        Tuple: A tuple with two Tensor (boxes, variances)

        boxes(Tensor): the output prior boxes of PriorBox.
        4-D tensor, the layout is [H, W, num_priors, 4].
        H is the height of input, W is the width of input,
        num_priors is the total box count of each position of input.

        variances(Tensor): the expanded variances of PriorBox.
        4-D tensor, the layput is [H, W, num_priors, 4].
        H is the height of input, W is the width of input
        num_priors is the total box count of each position of input
    """

    def _is_list_or_tuple_(data):
        return (isinstance(data, list) or isinstance(data, tuple))

    if not _is_list_or_tuple_(min_sizes):
        min_sizes = [min_sizes]
    if not _is_list_or_tuple_(aspect_ratios):
        aspect_ratios = [aspect_ratios]
    if not (_is_list_or_tuple_(steps) and len(steps) == 2):
        raise ValueError('steps should be a list or tuple ',
                         'with length 2, (step_width, step_height).')

    min_sizes = list(map(float, min_sizes))
    aspect_ratios = list(map(float, aspect_ratios))
    steps = list(map(float, steps))

    cur_max_sizes = None
    if max_sizes is not None and len(max_sizes) > 0 and max_sizes[0] > 0:
        if not _is_list_or_tuple_(max_sizes):
            max_sizes = [max_sizes]
        cur_max_sizes = list(map(float, max_sizes))

    # Get dimensions
    _, _, height, width = input.shape
    img_height, img_width = image.shape[2], image.shape[3]
    device = input.device
    dtype = input.dtype

    # Calculate steps
    step_w = steps[0] if steps[0] > 0 else img_width / width
    step_h = steps[1] if steps[1] > 0 else img_height / height

    # Prepare aspect ratios
    if flip:
        aspect_ratios = aspect_ratios + [1.0 / ar for ar in aspect_ratios if ar != 1.0]

    # Calculate number of priors
    num_priors = len(min_sizes) * len(aspect_ratios)
    if cur_max_sizes:
        num_priors += len(cur_max_sizes)

    # Generate prior boxes
    boxes = []
    for h in range(height):
        for w in range(width):
            center_x = (w + offset) * step_w
            center_y = (h + offset) * step_h

            for min_size in min_sizes:
                for ar in aspect_ratios:
                    box_w = min_size * np.sqrt(ar)
                    box_h = min_size / np.sqrt(ar)

                    xmin = (center_x - box_w / 2.) / img_width
                    ymin = (center_y - box_h / 2.) / img_height
                    xmax = (center_x + box_w / 2.) / img_width
                    ymax = (center_y + box_h / 2.) / img_height

                    boxes.append([xmin, ymin, xmax, ymax])

                # Add max size boxes
                if cur_max_sizes:
                    for max_size in cur_max_sizes:
                        box_w = box_h = np.sqrt(min_size * max_size)

                        xmin = (center_x - box_w / 2.) / img_width
                        ymin = (center_y - box_h / 2.) / img_height
                        xmax = (center_x + box_w / 2.) / img_width
                        ymax = (center_y + box_h / 2.) / img_height

                        boxes.append([xmin, ymin, xmax, ymax])

    boxes = torch.tensor(boxes, dtype=dtype, device=device)
    boxes = boxes.view(height, width, num_priors, 4)

    # Clip boxes if needed
    if clip:
        boxes = torch.clamp(boxes, 0., 1.)

    # Generate variances
    variances = torch.tensor(variance, dtype=dtype, device=device)
    variances = variances.view(1, 1, 1, 4).expand(height, width, num_priors, 4)

    return boxes, variances


def multiclass_nms(bboxes,
                   scores,
                   score_threshold,
                   nms_top_k,
                   keep_top_k,
                   nms_threshold=0.3,
                   normalized=True,
                   nms_eta=1.,
                   background_label=-1,
                   return_index=False,
                   return_rois_num=True,
                   rois_num=None,
                   name=None):
    """
    This operator is to do multi-class non maximum suppression (NMS) on
    boxes and scores.
    In the NMS step, this operator greedily selects a subset of detection bounding
    boxes that have high scores larger than score_threshold, if providing this
    threshold, then selects the largest nms_top_k confidences scores if nms_top_k
    is larger than -1. Then this operator pruns away boxes that have high IOU
    (intersection over union) overlap with already selected boxes by adaptive
    threshold NMS based on parameters of nms_threshold and nms_eta.
    Aftern NMS step, at most keep_top_k number of total bboxes are to be kept
    per image if keep_top_k is larger than -1.
    Args:
        bboxes (Tensor): Two types of bboxes are supported:
                           1. (Tensor) A 3-D Tensor with shape
                           [N, M, 4 or 8 16 24 32] represents the
                           predicted locations of M bounding bboxes,
                           N is the batch size. Each bounding box has four
                           coordinate values and the layout is
                           [xmin, ymin, xmax, ymax], when box size equals to 4.
                           2. (LoDTensor) A 3-D Tensor with shape [M, C, 4]
                           M is the number of bounding boxes, C is the
                           class number
        scores (Tensor): Two types of scores are supported:
                           1. (Tensor) A 3-D Tensor with shape [N, C, M]
                           represents the predicted confidence predictions.
                           N is the batch size, C is the class number, M is
                           number of bounding boxes. For each category there
                           are total M scores which corresponding M bounding
                           boxes. Please note, M is equal to the 2nd dimension
                           of BBoxes.
                           2. (LoDTensor) A 2-D LoDTensor with shape [M, C].
                           M is the number of bbox, C is the class number.
                           In this case, input BBoxes should be the second
                           case with shape [M, C, 4].
        background_label (int): The index of background label, the background
                                label will be ignored. If set to -1, then all
                                categories will be considered. Default: 0
        score_threshold (float): Threshold to filter out bounding boxes with
                                 low confidence score. If not provided,
                                 consider all boxes.
        nms_top_k (int): Maximum number of detections to be kept according to
                         the confidences after the filtering detections based
                         on score_threshold.
        nms_threshold (float): The threshold to be used in NMS. Default: 0.3
        nms_eta (float): The threshold to be used in NMS. Default: 1.0
        keep_top_k (int): Number of total bboxes to be kept per image after NMS
                          step. -1 means keeping all bboxes after NMS step.
        normalized (bool): Whether detections are normalized. Default: True
        return_index(bool): Whether return selected index. Default: False
        rois_num(Tensor): 1-D Tensor contains the number of RoIs in each image. 
            The shape is [B] and data type is int32. B is the number of images.
            If it is not None then return a list of 1-D Tensor. Each element 
            is the output RoIs' number of each image on the corresponding level
            and the shape is [B]. None by default.
        name(str): Name of the multiclass nms op. Default: None.
    Returns:
        A tuple with two Variables: (Out, Index) if return_index is True,
        otherwise, a tuple with one Variable(Out) is returned.
        Out: A 2-D LoDTensor with shape [No, 6] represents the detections.
        Each row has 6 values: [label, confidence, xmin, ymin, xmax, ymax]
        or A 2-D LoDTensor with shape [No, 10] represents the detections.
        Each row has 10 values: [label, confidence, x1, y1, x2, y2, x3, y3,
        x4, y4]. No is the total number of detections.
        If all images have not detected results, all elements in LoD will be
        0, and output tensor is empty (None).
        Index: Only return when return_index is True. A 2-D LoDTensor with
        shape [No, 1] represents the selected index which type is Integer.
        The index is the absolute value cross batches. No is the same number
        as Out. If the index is used to gather other attribute such as age,
        one needs to reshape the input(N, M, 1) to (N * M, 1) as first, where
        N is the batch size and M is the number of boxes.
    Examples:
        .. code-block:: python

            import paddle
            from ppdet.modeling import ops
            boxes = paddle.static.data(name='bboxes', shape=[81, 4],
                                      dtype='float32', lod_level=1)
            scores = paddle.static.data(name='scores', shape=[81],
                                      dtype='float32', lod_level=1)
            out, index = ops.multiclass_nms(bboxes=boxes,
                                            scores=scores,
                                            background_label=0,
                                            score_threshold=0.5,
                                            nms_top_k=400,
                                            nms_threshold=0.3,
                                            keep_top_k=200,
                                            normalized=False,
                                            return_index=True)
    """
    if not TORCHVISION_AVAILABLE:
        raise RuntimeError("torchvision is required for multiclass_nms")

    device = bboxes.device
    dtype = bboxes.dtype

    # Handle different input formats
    if bboxes.dim() == 3:  # [N, M, 4] format
        batch_size = bboxes.shape[0]
        all_outputs = []
        all_indices = []
        all_rois_num = []

        for i in range(batch_size):
            batch_boxes = bboxes[i]  # [M, 4]
            batch_scores = scores[i]  # [C, M] or [M, C]

            if batch_scores.shape[0] != batch_boxes.shape[0]:
                batch_scores = batch_scores.transpose(0, 1)  # [M, C]

            # Filter by score threshold
            max_scores, labels = torch.max(batch_scores, dim=1)
            valid_mask = max_scores > score_threshold

            if background_label >= 0:
                valid_mask = valid_mask & (labels != background_label)

            if not valid_mask.any():
                # No valid detections
                empty_output = torch.zeros((0, 6), dtype=dtype, device=device)
                all_outputs.append(empty_output)
                all_indices.append(torch.zeros((0, 1), dtype=torch.int32, device=device))
                all_rois_num.append(torch.tensor([0], dtype=torch.int32, device=device))
                continue

            valid_boxes = batch_boxes[valid_mask]
            valid_scores = batch_scores[valid_mask]
            valid_labels = labels[valid_mask]

            # Apply NMS per class
            keep_indices = []
            for class_id in torch.unique(valid_labels):
                if class_id == background_label:
                    continue

                class_mask = valid_labels == class_id
                class_boxes = valid_boxes[class_mask]
                class_scores = valid_scores[class_mask, class_id]

                if len(class_boxes) == 0:
                    continue

                # Apply torchvision NMS
                keep = nms(class_boxes, class_scores, nms_threshold)

                # Apply nms_top_k limit
                if nms_top_k > 0 and len(keep) > nms_top_k:
                    keep = keep[:nms_top_k]

                # Convert back to original indices
                class_indices = torch.nonzero(class_mask, as_tuple=False).squeeze(1)
                keep_indices.extend(class_indices[keep].tolist())

            if len(keep_indices) == 0:
                empty_output = torch.zeros((0, 6), dtype=dtype, device=device)
                all_outputs.append(empty_output)
                all_indices.append(torch.zeros((0, 1), dtype=torch.int32, device=device))
                all_rois_num.append(torch.tensor([0], dtype=torch.int32, device=device))
                continue

            keep_indices = torch.tensor(keep_indices, device=device)

            # Apply keep_top_k limit
            if keep_top_k > 0 and len(keep_indices) > keep_top_k:
                final_scores = torch.max(valid_scores[keep_indices], dim=1)[0]
                _, top_indices = torch.topk(final_scores, keep_top_k)
                keep_indices = keep_indices[top_indices]

            # Format output: [label, confidence, xmin, ymin, xmax, ymax]
            final_boxes = valid_boxes[keep_indices]
            final_scores = torch.max(valid_scores[keep_indices], dim=1)[0]
            final_labels = valid_labels[keep_indices].float()

            output = torch.cat([
                final_labels.unsqueeze(1),
                final_scores.unsqueeze(1),
                final_boxes
            ], dim=1)

            all_outputs.append(output)
            all_indices.append(keep_indices.unsqueeze(1).int())
            all_rois_num.append(torch.tensor([len(output)], dtype=torch.int32, device=device))

        # Concatenate all outputs
        if all_outputs:
            output = torch.cat(all_outputs, dim=0)
            index = torch.cat(all_indices, dim=0) if return_index else None
            nms_rois_num = torch.cat(all_rois_num, dim=0) if return_rois_num else None
        else:
            output = torch.zeros((0, 6), dtype=dtype, device=device)
            index = torch.zeros((0, 1), dtype=torch.int32, device=device) if return_index else None
            nms_rois_num = torch.zeros((batch_size,), dtype=torch.int32, device=device) if return_rois_num else None

    else:
        # Handle 2D input format [M, 4] boxes and [M, C] scores
        raise NotImplementedError("2D input format not implemented yet")

    return output, nms_rois_num, index


def matrix_nms(bboxes,
               scores,
               score_threshold,
               post_threshold,
               nms_top_k,
               keep_top_k,
               use_gaussian=False,
               gaussian_sigma=2.,
               background_label=0,
               normalized=True,
               return_index=False,
               return_rois_num=True,
               name=None):
    """
    **Matrix NMS**
    This operator does matrix non maximum suppression (NMS).
    First selects a subset of candidate bounding boxes that have higher scores
    than score_threshold (if provided), then the top k candidate is selected if
    nms_top_k is larger than -1. Score of the remaining candidate are then
    decayed according to the Matrix NMS scheme.
    Aftern NMS step, at most keep_top_k number of total bboxes are to be kept
    per image if keep_top_k is larger than -1.
    Args:
        bboxes (Tensor): A 3-D Tensor with shape [N, M, 4] represents the
                           predicted locations of M bounding bboxes,
                           N is the batch size. Each bounding box has four
                           coordinate values and the layout is
                           [xmin, ymin, xmax, ymax], when box size equals to 4.
                           The data type is float32 or float64.
        scores (Tensor): A 3-D Tensor with shape [N, C, M]
                           represents the predicted confidence predictions.
                           N is the batch size, C is the class number, M is
                           number of bounding boxes. For each category there
                           are total M scores which corresponding M bounding
                           boxes. Please note, M is equal to the 2nd dimension
                           of BBoxes. The data type is float32 or float64.
        score_threshold (float): Threshold to filter out bounding boxes with
                                 low confidence score.
        post_threshold (float): Threshold to filter out bounding boxes with
                                low confidence score AFTER decaying.
        nms_top_k (int): Maximum number of detections to be kept according to
                         the confidences after the filtering detections based
                         on score_threshold.
        keep_top_k (int): Number of total bboxes to be kept per image after NMS
                          step. -1 means keeping all bboxes after NMS step.
        use_gaussian (bool): Use Gaussian as the decay function. Default: False
        gaussian_sigma (float): Sigma for Gaussian decay function. Default: 2.0
        background_label (int): The index of background label, the background
                                label will be ignored. If set to -1, then all
                                categories will be considered. Default: 0
        normalized (bool): Whether detections are normalized. Default: True
        return_index(bool): Whether return selected index. Default: False
        return_rois_num(bool): whether return rois_num. Default: True
        name(str): Name of the matrix nms op. Default: None.
    Returns:
        A tuple with three Tensor: (Out, Index, RoisNum) if return_index is True,
        otherwise, a tuple with two Tensor (Out, RoisNum) is returned.
        Out (Tensor): A 2-D Tensor with shape [No, 6] containing the
             detection results.
             Each row has 6 values: [label, confidence, xmin, ymin, xmax, ymax]
             (After version 1.3, when no boxes detected, the lod is changed
             from {0} to {1})
        Index (Tensor): A 2-D Tensor with shape [No, 1] containing the
            selected indices, which are absolute values cross batches.
        rois_num (Tensor): A 1-D Tensor with shape [N] containing 
            the number of detected boxes in each image.
    Examples:
        .. code-block:: python
            import paddle
            from ppdet.modeling import ops
            boxes = paddle.static.data(name='bboxes', shape=[None,81, 4],
                                      dtype='float32', lod_level=1)
            scores = paddle.static.data(name='scores', shape=[None,81],
                                      dtype='float32', lod_level=1)
            out = ops.matrix_nms(bboxes=boxes, scores=scores, background_label=0,
                                 score_threshold=0.5, post_threshold=0.1,
                                 nms_top_k=400, keep_top_k=200, normalized=False)
    """
    # Simplified matrix NMS implementation
    # For now, fall back to regular multiclass_nms
    return multiclass_nms(
        bboxes=bboxes,
        scores=scores,
        score_threshold=score_threshold,
        nms_top_k=nms_top_k,
        keep_top_k=keep_top_k,
        nms_threshold=post_threshold,
        normalized=normalized,
        background_label=background_label,
        return_index=return_index,
        return_rois_num=return_rois_num
    )


def box_coder(prior_box,
              prior_box_var,
              target_box,
              code_type="encode_center_size",
              box_normalized=True,
              axis=0,
              name=None):
    r"""
    **Box Coder Layer**
    Encode/Decode the target bounding box with the priorbox information.
    
    The Encoding schema described below:
    .. math::
        ox = (tx - px) / pw / pxv
        oy = (ty - py) / ph / pyv
        ow = \log(\abs(tw / pw)) / pwv 
        oh = \log(\abs(th / ph)) / phv 
    The Decoding schema described below:
    
    .. math::
  
        ox = (pw * pxv * tx * + px) - tw / 2
        oy = (ph * pyv * ty * + py) - th / 2
        ow = \exp(pwv * tw) * pw + tw / 2
        oh = \exp(phv * th) * ph + th / 2   
    where `tx`, `ty`, `tw`, `th` denote the target box's center coordinates, 
    width and height respectively. Similarly, `px`, `py`, `pw`, `ph` denote 
    the priorbox's (anchor) center coordinates, width and height. `pxv`, 
    `pyv`, `pwv`, `phv` denote the variance of the priorbox and `ox`, `oy`, 
    `ow`, `oh` denote the encoded/decoded coordinates, width and height. 
    During Box Decoding, two modes for broadcast are supported. Say target 
    box has shape [N, M, 4], and the shape of prior box can be [N, 4] or 
    [M, 4]. Then prior box will broadcast to target box along the 
    assigned axis. 

    Args:
        prior_box(Tensor): Box list prior_box is a 2-D Tensor with shape 
            [M, 4] holds M boxes and data type is float32 or float64. Each box
            is represented as [xmin, ymin, xmax, ymax], [xmin, ymin] is the 
            left top coordinate of the anchor box, if the input is image feature
            map, they are close to the origin of the coordinate system. 
            [xmax, ymax] is the right bottom coordinate of the anchor box.       
        prior_box_var(List|Tensor|None): prior_box_var supports three types 
            of input. One is Tensor with shape [M, 4] which holds M group and 
            data type is float32 or float64. The second is list consist of 
            4 elements shared by all boxes and data type is float32 or float64. 
            Other is None and not involved in calculation. 
        target_box(Tensor): This input can be a 2-D LoDTensor with shape 
            [N, 4] when code_type is 'encode_center_size'. This input also can 
            be a 3-D Tensor with shape [N, M, 4] when code_type is 
            'decode_center_size'. Each box is represented as 
            [xmin, ymin, xmax, ymax]. The data type is float32 or float64. 
        code_type(str): The code type used with the target box. It can be
            `encode_center_size` or `decode_center_size`. `encode_center_size` 
            by default.
        box_normalized(bool): Whether treat the priorbox as a normalized box.
            Set true by default.
        axis(int): Which axis in PriorBox to broadcast for box decode, 
            for example, if axis is 0 and TargetBox has shape [N, M, 4] and 
            PriorBox has shape [M, 4], then PriorBox will broadcast to [N, M, 4]
            for decoding. It is only valid when code type is 
            `decode_center_size`. Set 0 by default. 
        name(str, optional): For detailed information, please refer 
            to :ref:`api_guide_Name`. Usually name is no need to set and 
            None by default. 

    Returns:
        Tensor:
        output_box(Tensor): When code_type is 'encode_center_size', the 
        output tensor of box_coder_op with shape [N, M, 4] representing the 
        result of N target boxes encoded with M Prior boxes and variances. 
        When code_type is 'decode_center_size', N represents the batch size 
        and M represents the number of decoded boxes.

    Examples:
 
        .. code-block:: python
 
            import paddle
            from ppdet.modeling import ops
            paddle.enable_static()
            # For encode
            prior_box_encode = paddle.static.data(name='prior_box_encode',
                                  shape=[512, 4],
                                  dtype='float32')
            target_box_encode = paddle.static.data(name='target_box_encode',
                                   shape=[81, 4],
                                   dtype='float32')
            output_encode = ops.box_coder(prior_box=prior_box_encode,
                                    prior_box_var=[0.1,0.1,0.2,0.2],
                                    target_box=target_box_encode,
                                    code_type="encode_center_size")
            # For decode
            prior_box_decode = paddle.static.data(name='prior_box_decode',
                                  shape=[512, 4],
                                  dtype='float32')
            target_box_decode = paddle.static.data(name='target_box_decode',
                                   shape=[512, 81, 4],
                                   dtype='float32')
            output_decode = ops.box_coder(prior_box=prior_box_decode,
                                    prior_box_var=[0.1,0.1,0.2,0.2],
                                    target_box=target_box_decode,
                                    code_type="decode_center_size",
                                    box_normalized=False,
                                    axis=1)
    """
    # Simplified box coder - placeholder implementation
    # This function needs proper implementation for production use
    raise NotImplementedError("box_coder function needs to be implemented for PyTorch")


def generate_proposals(scores,
                       bbox_deltas,
                       im_shape,
                       anchors,
                       variances,
                       pre_nms_top_n=6000,
                       post_nms_top_n=1000,
                       nms_thresh=0.5,
                       min_size=0.1,
                       eta=1.0,
                       pixel_offset=False,
                       return_rois_num=False,
                       name=None):
    """
    **Generate proposal Faster-RCNN**
    This operation proposes RoIs according to each box with their
    probability to be a foreground object and 
    the box can be calculated by anchors. Bbox_deltais and scores
    to be an object are the output of RPN. Final proposals
    could be used to train detection net.
    For generating proposals, this operation performs following steps:
    1. Transposes and resizes scores and bbox_deltas in size of
       (H*W*A, 1) and (H*W*A, 4)
    2. Calculate box locations as proposals candidates. 
    3. Clip boxes to image
    4. Remove predicted boxes with small area. 
    5. Apply NMS to get final proposals as output.
    Args:
        scores(Tensor): A 4-D Tensor with shape [N, A, H, W] represents
            the probability for each box to be an object.
            N is batch size, A is number of anchors, H and W are height and
            width of the feature map. The data type must be float32.
        bbox_deltas(Tensor): A 4-D Tensor with shape [N, 4*A, H, W]
            represents the difference between predicted box location and
            anchor location. The data type must be float32.
        im_shape(Tensor): A 2-D Tensor with shape [N, 2] represents H, W, the
            origin image size or input size. The data type can be float32 or 
            float64.
        anchors(Tensor):   A 4-D Tensor represents the anchors with a layout
            of [H, W, A, 4]. H and W are height and width of the feature map,
            num_anchors is the box count of each position. Each anchor is
            in (xmin, ymin, xmax, ymax) format an unnormalized. The data type must be float32.
        variances(Tensor): A 4-D Tensor. The expanded variances of anchors with a layout of
            [H, W, num_priors, 4]. Each variance is in
            (xcenter, ycenter, w, h) format. The data type must be float32.
        pre_nms_top_n(float): Number of total bboxes to be kept per
            image before NMS. The data type must be float32. `6000` by default.
        post_nms_top_n(float): Number of total bboxes to be kept per
            image after NMS. The data type must be float32. `1000` by default.
        nms_thresh(float): Threshold in NMS. The data type must be float32. `0.5` by default.
        min_size(float): Remove predicted boxes with either height or
            width < min_size. The data type must be float32. `0.1` by default.
        eta(float): Apply in adaptive NMS, if adaptive `threshold > 0.5`,
            `adaptive_threshold = adaptive_threshold * eta` in each iteration.
        return_rois_num(bool): When setting True, it will return a 1D Tensor with shape [N, ] that includes Rois's 
            num of each image in one batch. The N is the image's num. For example, the tensor has values [4,5] that represents
            the first image has 4 Rois, the second image has 5 Rois. It only used in rcnn model. 
            'False' by default. 
        name(str, optional): For detailed information, please refer 
            to :ref:`api_guide_Name`. Usually name is no need to set and 
            None by default. 

    Returns:
        tuple:
        A tuple with format ``(rpn_rois, rpn_roi_probs)``.
        - **rpn_rois**: The generated RoIs. 2-D Tensor with shape ``[N, 4]`` while ``N`` is the number of RoIs. The data type is the same as ``scores``.
        - **rpn_roi_probs**: The scores of generated RoIs. 2-D Tensor with shape ``[N, 1]`` while ``N`` is the number of RoIs. The data type is the same as ``scores``.

    Examples:
        .. code-block:: python
        
            import paddle
            from ppdet.modeling import ops
            paddle.enable_static()
            scores = paddle.static.data(name='scores', shape=[None, 4, 5, 5], dtype='float32')
            bbox_deltas = paddle.static.data(name='bbox_deltas', shape=[None, 16, 5, 5], dtype='float32')
            im_shape = paddle.static.data(name='im_shape', shape=[None, 2], dtype='float32')
            anchors = paddle.static.data(name='anchors', shape=[None, 5, 4, 4], dtype='float32')
            variances = paddle.static.data(name='variances', shape=[None, 5, 10, 4], dtype='float32')
            rois, roi_probs = ops.generate_proposals(scores, bbox_deltas,
                         im_shape, anchors, variances)
    """
    # Simplified generate_proposals - placeholder implementation
    # This function needs proper implementation for production use
    raise NotImplementedError("generate_proposals function needs to be implemented for PyTorch")


def sigmoid_cross_entropy_with_logits(input,
                                      label,
                                      ignore_index=-100,
                                      normalize=False):
    output = F.binary_cross_entropy_with_logits(input, label, reduction='none')
    mask_tensor = (label != ignore_index).float()
    output = output * mask_tensor
    if normalize:
        sum_valid_mask = torch.sum(mask_tensor)
        output = output / sum_valid_mask
    return output


def smooth_l1(input, label, inside_weight=None, outside_weight=None,
              sigma=None):
    input_new = input * inside_weight
    label_new = label * inside_weight
    delta = 1 / (sigma * sigma)
    out = F.smooth_l1_loss(input_new, label_new, reduction='none', beta=delta)
    out = out * outside_weight
    out = out / delta
    out = out.view(out.shape[0], -1)
    out = torch.sum(out, dim=1)
    return out


def channel_shuffle(x, groups):
    batch_size, num_channels, height, width = x.shape[0:4]
    assert num_channels % groups == 0, 'num_channels should be divisible by groups'
    channels_per_group = num_channels // groups
    x = x.view(batch_size, groups, channels_per_group, height, width)
    x = x.permute(0, 2, 1, 3, 4)
    x = x.contiguous().view(batch_size, num_channels, height, width)
    return x


def get_static_shape(tensor):
    return tensor.shape
