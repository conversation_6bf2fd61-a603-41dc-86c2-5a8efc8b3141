#!/usr/bin/env python3
"""
Standalone test for YAML custom tags
"""
import yaml

# Copy the relevant code from yaml_helpers.py
def _make_python_constructor(cls):
    def python_constructor(loader, node):
        if hasattr(yaml, 'SequenceNode') and isinstance(node, yaml.SequenceNode):
            args = loader.construct_sequence(node, deep=True)
            return cls(*args)
        else:
            kwargs = loader.construct_mapping(node, deep=True)
            try:
                return cls(**kwargs)
            except Exception as ex:
                print(f"Error when construct {cls.__name__} instance from yaml config: {ex}")
                raise ex
    return python_constructor

def serializable(cls):
    """Add loader and dumper for given class"""
    yaml.add_constructor(u'!{}'.format(cls.__name__), _make_python_constructor(cls))
    return cls

# Define simple test classes (matching our converted classes)
@serializable
class PiecewiseDecay(object):
    def __init__(self, gamma=1.0, milestones=[100], use_warmup=True):
        self.gamma = gamma
        self.milestones = milestones
        self.use_warmup = use_warmup
    
    def __repr__(self):
        return f"PiecewiseDecay(gamma={self.gamma}, milestones={self.milestones}, use_warmup={self.use_warmup})"

@serializable  
class LinearWarmup(object):
    def __init__(self, start_factor=0.001, steps=2000):
        self.start_factor = start_factor
        self.steps = steps
    
    def __repr__(self):
        return f"LinearWarmup(start_factor={self.start_factor}, steps={self.steps})"

def test_yaml_parsing():
    """Test YAML parsing with custom tags"""
    yaml_content = """
LearningRate:
  base_lr: 0.0001
  schedulers:
  - !PiecewiseDecay
    gamma: 1.0
    milestones: [100]
    use_warmup: true
  - !LinearWarmup
    start_factor: 0.001
    steps: 2000
"""
    
    print("🔍 测试 YAML 解析...")
    
    try:
        # Parse the YAML
        config = yaml.load(yaml_content, Loader=yaml.Loader)
        
        print("✅ YAML 解析成功!")
        print(f"📊 解析结果: {config}")
        
        # Check if the custom objects were created correctly
        schedulers = config['LearningRate']['schedulers']
        print(f"📋 调度器数量: {len(schedulers)}")
        
        for i, scheduler in enumerate(schedulers):
            print(f"   调度器 {i+1}: {scheduler}")
            print(f"   类型: {type(scheduler)}")
            if hasattr(scheduler, '__dict__'):
                print(f"   参数: {scheduler.__dict__}")
        
        print("\n🎉 YAML 自定义标签解析测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ YAML 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_yaml_parsing()
    exit(0 if success else 1)