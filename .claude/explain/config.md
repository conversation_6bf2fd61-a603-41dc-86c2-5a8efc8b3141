# configs 在 DAMSDet 项目中的作用总结

## 概述
configs 目录是 DAMSDet 项目的配置管理中心，采用 YAML 格式文件组织配置，支持模块化配置继承和组合。

## 目录结构
```
configs/
├── damsdet/              # 模型架构配置
│   ├── _base_/           # 基础配置模块
│   │   ├── damsdet_r50vd.yml        # 模型基础架构
│   │   ├── damsdet_r50vd_1024.yml   # 1024分辨率版本
│   │   ├── damsdet_reader.yml       # 数据读取配置
│   │   ├── damsdet_reader_1024.yml  # 1024分辨率数据读取
│   │   └── optimizer.yml           # 优化器配置
│   ├── damsdet_r50vd_flir.yml       # FLIR数据集配置
│   ├── damsdet_r50vd_llvip.yml      # LLVIP数据集配置
│   ├── damsdet_r50vd_m3fd.yml       # M3FD数据集配置
│   ├── damsdet_r50vd_m3fd_pytorch.yml # M3FD PyTorch版本
│   ├── damsdet_r50vd_vedai.yml      # VEDAI数据集配置
│   └── damsdet_r50vd_vedai_pytorch.yml # VEDAI PyTorch版本
├── datasets/             # 数据集配置
│   ├── coco_detection_FLIR_align.yml
│   ├── coco_detection_LLVIP.yml
│   ├── coco_detection_VEDAI.yml
│   └── coco_detection_m3fd.yml
├── runtime_flir.yml      # FLIR运行时配置
├── runtime_llvip.yml     # LLVIP运行时配置
├── runtime_m3fd.yml      # M3FD运行时配置
└── runtime_vedai.yml     # VEDAI运行时配置
```

## 主要作用

### 1. 模型架构配置 (_base_)
- **damsdet_r50vd.yml**: 定义 DAMSDet 模型的基础架构
  - 设置骨干网络（ResNet-50）、颈部网络（HybridEncoder）
  - 配置变压器（DAMS_DETR_Transformer）参数
  - 定义检测头（DINOHead）和后处理（DETRPostProcess）
  
- **optimizer.yml**: 优化器和学习率配置
  - 训练轮数（epoch: 50）
  - 基础学习率（base_lr: 0.0001）
  - 学习率调度策略（PiecewiseDecay + LinearWarmup）
  - 优化器类型（AdamW）和正则化设置

### 2. 数据集配置 (datasets/)
- 定义每个数据集的特定配置：
  - 数据集路径（dataset_dir）
  - 图像目录（vis_image_dir, ir_image_dir）
  - 标注文件路径（anno_path）
  - 数据集类别数（num_classes）
  - 数据增强和预处理参数

### 3. 运行时配置 (runtime_*.yml)
- 硬件配置（GPU/XPU/MLU/NPU）
- 日志记录频率（log_iter）
- 模型保存目录（save_dir）
- 快照保存间隔（snapshot_epoch）
- 模型导出配置

### 4. 组合配置 (damsdet/*.yml)
通过 _BASE_ 继承机制组合基础配置：
```yaml
_BASE_: [
  '../datasets/coco_detection_m3fd.yml',  # 数据集配置
  '../runtime_m3fd.yml',                  # 运行时配置
  '_base_/optimizer.yml',                 # 优化器配置
  '_base_/damsdet_r50vd.yml',             # 模型架构配置
  '_base_/damsdet_reader.yml',            # 数据读取配置
]
```

## 配置系统优势

### 1. 模块化设计
- 将配置按功能分离，便于维护和修改
- 支持配置复用，避免重复定义

### 2. 继承机制
- 使用 _BASE_ 实现配置继承
- 可以覆盖和扩展基础配置

### 3. 数据集适配
- 为不同数据集提供专门配置
- 支持多光谱数据的特殊需求

### 4. 灵活性
- 支持不同分辨率版本（1024 vs 640）
- 支持不同框架版本（PaddlePaddle vs PyTorch）
- 可以轻松调整超参数

## 使用方式

### 训练命令
```bash
python tools/train.py -c configs/damsdet/damsdet_r50vd_m3fd.yml \
  -o pretrain_weights=coco_pretrain_weights.pdparams --eval
```

### 评估命令
```bash
python tools/eval.py -c configs/damsdet/damsdet_r50vd_m3fd.yml \
  --classwise -o weights=output/M3FD/damsdet_r50vd_m3fd/best_model
```

## 关键配置参数

### 模型参数
- `hidden_dim`: 256 - 特征维度
- `num_queries`: 300 - 查询数量
- `num_decoder_layers`: 6 - 解码器层数
- `num_denoising`: 100 - 去噪训练样本数

### 训练参数
- `epoch`: 50 - 训练轮数
- `base_lr`: 0.0001 - 基础学习率
- `batch_size`: 根据数据集不同而设置

### 数据参数
- `num_classes`: 不同数据集类别数不同（M3FD: 6）
- `eval_size`: [640, 640] - 评估尺寸
- 多光谱数据路径配置

## 总结

configs 目录是 DAMSDet 项目的核心配置系统，通过模块化的 YAML 配置文件实现了：

1. **统一管理**: 集中管理所有训练、评估、推理参数
2. **灵活适配**: 支持多个数据集和不同分辨率
3. **易于维护**: 模块化设计便于修改和扩展
4. **版本控制**: 支持 PaddlePaddle 和 PyTorch 两种框架

这种配置系统设计使得 DAMSDet 能够灵活地适应不同的应用场景和数据集需求，同时保持代码的整洁和可维护性。